/**
 * CC to AP Custom Fields Processor
 *
 * Handles synchronization of custom fields from CliniCore (CC) to AutoPatient (AP) by:
 * 1. Fetching CC patient custom field data using the custom field IDs
 * 2. Filtering out standard contact fields to prevent conflicts
 * 3. Mapping CC custom field values to AP custom field format with proper Unicode handling
 * 4. Creating AP custom fields with appropriate data types if they don't exist
 * 5. Updating the AP contact with the synchronized custom field values
 *
 * Features:
 * - Unicode-aware field name matching for international characters
 * - Data type mapping between CC and AP custom field types
 * - Prevention of standard contact field conversion to custom fields
 * - Proper handling of multiple values in CC custom fields
 */

import type {
	APGetCustomFieldType,
	APPostCustomfieldType,
	GetCCCustomField,
	GetCCPatientCustomField,
	GetCCPatientType,
} from "@type";
import { apCustomfield, contactReq, patientReq } from "@/apiClient";
import { logApiError } from "@/utils/errorLogger";
import { logInfo, logError, logDebug } from "@/utils/logger";

/**
 * Custom field specific logging function
 * @param requestId - Request ID for correlation
 * @param action - Action being performed (e.g., "creating", "mapped", "skipped")
 * @param fieldName - Name of the field
 * @param data - Additional data to log
 */
function logCustomField(
	requestId: string,
	action: string,
	fieldName: string,
	data?: Record<string, unknown>
): void {
	logInfo(requestId, `Custom field ${action}: ${fieldName}`, data);
}

/**
 * Standard contact fields that should not be converted to custom fields
 * Based on PostAPContactType interface to prevent conflicts with core contact data
 */
const STANDARD_CONTACT_FIELDS = [
	"email",
	"phone",
	"name",
	"firstName",
	"lastName",
	"timezone",
	"dnd",
	"source",
	"assignedTo",
	"address1",
	"city",
	"state",
	"country",
	"postalCode",
	"tags",
	"dateOfBirth",
	"ssn",
	"gender",
	"customFields",
	// Common variations and translations
	"first name",
	"last name",
	"date of birth",
	"phone number",
	"email address",
	"postal code",
	"zip code",
	"address",
	"vorname",
	"nachname",
	"geburtsdatum",
	"telefon",
	"e-mail",
	"adresse",
	"postleitzahl",
];

/**
 * CC to AP data type mapping
 * Maps CliniCore custom field types to AutoPatient data types
 */
const CC_TO_AP_DATA_TYPE_MAPPING: Record<string, string> = {
	// Text-based fields
	text: "TEXT",
	textarea: "LARGE_TEXT",
	string: "TEXT",

	// Numeric fields
	number: "NUMERICAL",
	integer: "NUMERICAL",
	decimal: "FLOAT",
	float: "FLOAT",
	currency: "MONETORY",
	money: "MONETORY",

	// Contact fields
	phone: "PHONE",
	telephone: "PHONE",

	// Boolean fields
	boolean: "CHECKBOX",
	checkbox: "CHECKBOX",

	// Selection fields
	select: "SINGLE_OPTIONS",
	dropdown: "SINGLE_OPTIONS",
	radio: "SINGLE_OPTIONS",
	multiselect: "MULTIPLE_OPTIONS",

	// Date/Time fields
	date: "DATE",
	datetime: "DATE",
	time: "TIME",

	// File fields
	file: "FILE_UPLOAD",
	upload: "FILE_UPLOAD",
	attachment: "FILE_UPLOAD",

	// Signature
	signature: "SIGNATURE",

	// Default fallback
	default: "TEXT",
};

/**
 * Interface for custom field mapping result
 */
interface CustomFieldMapping {
	/** AP custom field ID */
	id: string;
	/** Field value to set */
	value: string;
}

/**
 * Normalize string for Unicode-aware comparison
 * Handles German Umlaut characters and other special characters
 */
function normalizeFieldName(name: string): string {
	return name
		.toLowerCase()
		.normalize("NFD")
		.replace(/[\u0300-\u036f]/g, "") // Remove diacritics
		.replace(/[^\w\s]/g, "") // Remove special characters except word chars and spaces
		.replace(/\s+/g, " ") // Normalize whitespace
		.trim();
}

/**
 * Check if two field names match using Unicode-aware comparison
 */
function fieldNamesMatch(name1: string, name2: string): boolean {
	const normalized1 = normalizeFieldName(name1);
	const normalized2 = normalizeFieldName(name2);
	return normalized1 === normalized2;
}

/**
 * Check if a field name represents a standard contact field
 */
function isStandardContactField(fieldName: string): boolean {
	const normalizedFieldName = normalizeFieldName(fieldName);
	return STANDARD_CONTACT_FIELDS.some(standardField =>
		normalizeFieldName(standardField) === normalizedFieldName
	);
}

/**
 * Map CC custom field type to AP data type
 */
function mapCcToApDataType(ccFieldType: string): string {
	const normalizedType = ccFieldType.toLowerCase().trim();
	return CC_TO_AP_DATA_TYPE_MAPPING[normalizedType] || CC_TO_AP_DATA_TYPE_MAPPING.default;
}

/**
 * Extract and convert CC allowed values to AP textBoxListOptions format
 * @param ccField - CC custom field with allowedValues
 * @param currentValue - Current field value to include as an option if not in allowedValues
 * @returns Array of textBoxListOptions for AP custom field
 */
function extractTextBoxListOptions(
	ccField: GetCCCustomField,
	currentValue: string,
): { label: string; prefillValue: string }[] {
	const options: { label: string; prefillValue: string }[] = [];

	// Validate input parameters
	if (!ccField) {
		return options;
	}

	// Extract allowed values from CC field with enhanced validation
	if (ccField.allowedValues && Array.isArray(ccField.allowedValues) && ccField.allowedValues.length > 0) {
		for (const allowedValue of ccField.allowedValues) {
			// More robust validation of allowed values
			if (allowedValue &&
				typeof allowedValue.value === 'string' &&
				allowedValue.value.trim() !== "" &&
				allowedValue.value.trim().length > 0) {

				const trimmedValue = allowedValue.value.trim();
				// Avoid duplicate options
				const isDuplicate = options.some(opt =>
					opt.label.toLowerCase() === trimmedValue.toLowerCase()
				);

				if (!isDuplicate) {
					options.push({
						label: trimmedValue,
						prefillValue: trimmedValue,
					});
				}
			}
		}
	}

	// Process current value - handle comma-separated values properly
	if (currentValue && currentValue.trim() !== "") {
		const currentValueTrimmed = currentValue.trim();

		// Split comma-separated values and process each one
		const valueList = currentValueTrimmed.split(',').map(v => v.trim()).filter(v => v.length > 0);

		for (const value of valueList) {
			// Check if this value already exists in options
			const existingOption = options.find(
				option => option.label.toLowerCase() === value.toLowerCase()
			);

			if (!existingOption) {
				options.push({
					label: value,
					prefillValue: value,
				});
			}
		}
	}

	return options;
}

/**
 * Validate custom field data before creating AP custom field
 * @param fieldData - Custom field creation data
 * @param fieldName - Field name for logging
 * @returns Validation result with success flag and error message
 */
function validateCustomFieldForCreation(
	fieldData: APPostCustomfieldType,
	fieldName: string,
): { isValid: boolean; errorMessage?: string } {
	// Validate required fields
	if (!fieldData.name || fieldData.name.trim() === "") {
		return { isValid: false, errorMessage: "Field name is required" };
	}

	if (!fieldData.dataType || fieldData.dataType.trim() === "") {
		return { isValid: false, errorMessage: "Data type is required" };
	}

	// Validate options for dropdown/multi-select fields
	if (fieldData.dataType === "SINGLE_OPTIONS" || fieldData.dataType === "MULTIPLE_OPTIONS") {
		if (!fieldData.textBoxListOptions || !Array.isArray(fieldData.textBoxListOptions)) {
			return {
				isValid: false,
				errorMessage: "textBoxListOptions must be an array for SINGLE_OPTIONS/MULTIPLE_OPTIONS fields"
			};
		}

		if (fieldData.textBoxListOptions.length === 0) {
			return {
				isValid: false,
				errorMessage: "textBoxListOptions must contain at least 1 element for SINGLE_OPTIONS/MULTIPLE_OPTIONS fields"
			};
		}

		// Validate each option
		for (let i = 0; i < fieldData.textBoxListOptions.length; i++) {
			const option = fieldData.textBoxListOptions[i];
			if (!option || typeof option !== 'object') {
				return {
					isValid: false,
					errorMessage: `textBoxListOptions[${i}] must be an object`
				};
			}

			if (!option.label || typeof option.label !== 'string' || option.label.trim() === "") {
				return {
					isValid: false,
					errorMessage: `textBoxListOptions[${i}].label must be a non-empty string`
				};
			}

			if (!option.prefillValue || typeof option.prefillValue !== 'string' || option.prefillValue.trim() === "") {
				return {
					isValid: false,
					errorMessage: `textBoxListOptions[${i}].prefillValue must be a non-empty string`
				};
			}

			// Check for problematic characters or patterns in option labels
			const label = option.label.trim();
			if (label.includes(',') && label.split(',').length > 1) {
				return {
					isValid: false,
					errorMessage: `textBoxListOptions[${i}].label contains comma-separated values: "${label}". Each option should be a single value.`
				};
			}

			// Check for excessively long labels
			if (label.length > 100) {
				return {
					isValid: false,
					errorMessage: `textBoxListOptions[${i}].label is too long (${label.length} characters). Maximum 100 characters allowed.`
				};
			}
		}

		// Check for duplicate labels
		const labels = fieldData.textBoxListOptions.map(opt => opt.label.toLowerCase());
		const uniqueLabels = new Set(labels);
		if (labels.length !== uniqueLabels.size) {
			return {
				isValid: false,
				errorMessage: "textBoxListOptions contains duplicate labels"
			};
		}
	}

	return { isValid: true };
}

/**
 * Extract and combine multiple values from CC custom field
 * Handles different separation strategies based on field type
 */
function extractFieldValues(ccCustomField: GetCCPatientCustomField): string {
	if (!ccCustomField.values || ccCustomField.values.length === 0) {
		return "";
	}

	const values = ccCustomField.values
		.map(v => v.value)
		.filter((v): v is string => v != null && v.trim() !== "");

	if (values.length === 0) {
		return "";
	}

	// For single value, return as-is
	if (values.length === 1) {
		return values[0];
	}

	// For multiple values, choose separation strategy based on field type
	const fieldType = ccCustomField.field.type?.toLowerCase() || "";

	if (fieldType.includes("multiselect") || fieldType.includes("checkbox")) {
		// Use comma separation for multi-select fields
		return values.join(", ");
	} else if (fieldType.includes("textarea") || fieldType.includes("text")) {
		// Use newline separation for text areas
		return values.join("\n");
	} else {
		// Default to comma separation
		return values.join(", ");
	}
}

/**
 * Synchronize custom fields from CC patient to AP contact
 *
 * @param requestId - Request ID from Hono context for logging correlation
 * @param localPatientId - Local database patient ID for logging context
 * @param ccPatientData - CC patient data containing custom field IDs
 * @param apContactId - AP contact ID to update with custom fields
 * @returns Promise<void> - Completes sync or throws error
 */
export async function syncCcToApCustomFields(
	requestId: string,
	localPatientId: string,
	ccPatientData: GetCCPatientType,
	apContactId: string,
): Promise<void> {

	console.log(
		`[${requestId}] Starting custom field sync for CC Patient ${ccPatientData.id} -> AP Contact ${apContactId} (Local Patient ID: ${localPatientId})`,
	);

	// Step 1: Check if patient has custom fields
	if (!ccPatientData.customFields || ccPatientData.customFields.length === 0) {
		console.log(
			`[${requestId}] No custom fields found for CC patient ${ccPatientData.id}`,
		);
		return;
	}

	try {
		// Step 2: Fetch CC patient custom field data
		console.log(
			`[${requestId}] Fetching ${ccPatientData.customFields.length} custom fields from CC`,
		);
		const ccPatientCustomFields = await patientReq.customFields(
			ccPatientData.customFields,
		);

		if (!ccPatientCustomFields || ccPatientCustomFields.length === 0) {
			console.log(`[${requestId}] No custom field data returned from CC`);
			return;
		}

		// Step 3: Filter out excluded fields and extract valid field mappings
		const validCustomFields = await filterAndMapCustomFields(
			ccPatientCustomFields,
			requestId,
		);

		if (validCustomFields.length === 0) {
			console.log(
				`[${requestId}] No valid custom fields to sync after filtering`,
			);
			return;
		}

		// Step 4: Get all AP custom fields for mapping
		console.log(`[${requestId}] Fetching AP custom fields for mapping`);
		const apCustomFields = await apCustomfield.all();

		// Step 5: Map CC fields to AP format and create missing fields
		const apCustomFieldMappings = await mapToApCustomFields(
			validCustomFields,
			apCustomFields,
			requestId,
		);

		if (apCustomFieldMappings.length === 0) {
			console.log(`[${requestId}] No custom field mappings created`);
			return;
		}

		// Step 6: Update AP contact with custom fields
		console.log(
			`[${requestId}] Updating AP contact with ${apCustomFieldMappings.length} custom fields`,
		);
		await contactReq.update(apContactId, {
			customFields: apCustomFieldMappings,
		});

		console.log(`[${requestId}] Custom field sync completed successfully`);
	} catch (error) {
		console.error(`[${requestId}] Custom field sync failed:`, error);

		// Log the error but don't throw to avoid blocking main patient processing
		await logApiError(
			error as Error,
			requestId,
			"custom_field_sync",
			"cc_to_ap_sync",
			{
				ccPatientId: ccPatientData.id,
				apContactId,
				customFieldCount: ccPatientData.customFields?.length || 0,
			},
		);

		// Re-throw to let caller decide how to handle
		throw error;
	}
}

/**
 * Filter CC custom fields and extract valid field mappings
 * Excludes standard contact fields and empty values
 *
 * @param ccPatientCustomFields - CC patient custom field data
 * @param requestId - Request ID for logging
 * @returns Promise<Array> - Valid custom field data
 */
async function filterAndMapCustomFields(
	ccPatientCustomFields: GetCCPatientCustomField[],
	requestId: string,
): Promise<Array<{ field: GetCCCustomField; value: string }>> {
	const validFields: Array<{ field: GetCCCustomField; value: string }> = [];

	for (const ccCustomField of ccPatientCustomFields) {
		const fieldName = ccCustomField.field.name;
		const fieldLabel = ccCustomField.field.label;

		// Check if field is a standard contact field that should not be converted to custom field
		if (isStandardContactField(fieldName) || isStandardContactField(fieldLabel)) {
			console.log(
				`[${requestId}] Excluding standard contact field: ${fieldName} (${fieldLabel})`,
			);
			continue;
		}

		// Extract field value using improved multiple values handling
		const fieldValue = extractFieldValues(ccCustomField);

		if (!fieldValue || fieldValue.trim() === "") {
			console.log(`[${requestId}] Skipping field with empty value: ${fieldName}`);
			continue;
		}

		validFields.push({
			field: ccCustomField.field,
			value: fieldValue,
		});

		console.log(
			`[${requestId}] Valid field: ${fieldName} (${fieldLabel}) = ${fieldValue.substring(0, 100)}${fieldValue.length > 100 ? '...' : ''}`,
		);
	}

	return validFields;
}

/**
 * Map CC custom fields to AP custom field format
 * Creates missing AP custom fields with proper data types as needed
 *
 * @param validCustomFields - Filtered CC custom field data
 * @param apCustomFields - Existing AP custom fields
 * @param requestId - Request ID for logging
 * @returns Promise<CustomFieldMapping[]> - AP custom field mappings
 */
async function mapToApCustomFields(
	validCustomFields: Array<{ field: GetCCCustomField; value: string }>,
	apCustomFields: APGetCustomFieldType[],
	requestId: string,
): Promise<CustomFieldMapping[]> {
	const mappings: CustomFieldMapping[] = [];

	for (const { field, value } of validCustomFields) {
		try {
			// Try to find existing AP custom field using Unicode-aware matching
			let apCustomField = apCustomFields.find((apField) =>
				fieldNamesMatch(apField.name, field.name) ||
				fieldNamesMatch(apField.name, field.label)
			);

			// Create AP custom field if it doesn't exist
			if (!apCustomField) {
				const mappedDataType = mapCcToApDataType(field.type);

				console.log(
					`[${requestId}] Creating new AP custom field: ${field.label} (type: ${field.type} -> ${mappedDataType})`,
				);

				const createData: APPostCustomfieldType = {
					name: field.label, // Use label as it's more user-friendly
					dataType: mappedDataType,
					model: "contact", // Set model to contact as required
				};

				// Add conditional properties based on data type
				if (mappedDataType === "SINGLE_OPTIONS" || mappedDataType === "MULTIPLE_OPTIONS") {
					// Extract allowed values from CC field and convert to AP format
					const textBoxListOptions = extractTextBoxListOptions(field, value);

					if (textBoxListOptions.length > 0) {
						createData.textBoxListOptions = textBoxListOptions;

						// Validate the complete field data before API call
						const validation = validateCustomFieldForCreation(createData, field.label);

						if (validation.isValid) {
							const optionLabels = textBoxListOptions.map(opt => opt.label).join(", ");
							logCustomField(requestId, "options_added", field.label, {
								optionCount: textBoxListOptions.length,
								options: optionLabels
							});
						} else {
							// Validation failed, fall back to TEXT type
							logCustomField(requestId, "validation_failed", field.label, {
								error: validation.errorMessage,
								fallbackType: "TEXT"
							});
							createData.dataType = "TEXT";
							delete createData.textBoxListOptions;
						}
					} else {
						// If no options can be created, fall back to TEXT type to avoid API error
						logCustomField(requestId, "no_options_fallback", field.label, {
							reason: "No valid options available",
							fallbackType: "TEXT",
							ccAllowedValues: field.allowedValues?.length || 0,
							currentValue: value ? "present" : "empty"
						});
						createData.dataType = "TEXT";
					}
				}

				// Final validation before API call
				const finalValidation = validateCustomFieldForCreation(createData, field.label);
				if (!finalValidation.isValid) {
					logCustomField(requestId, "skipped", field.label, {
						reason: "Final validation failed",
						error: finalValidation.errorMessage
					});
					continue; // Skip this field and continue with the next one
				}

				// Log the exact data being sent to AP API for debugging
				logCustomField(requestId, "creating", field.label, {
					originalType: field.type,
					mappedType: createData.dataType,
					mappedName: createData.name
				});

				// Attempt to create the custom field with error handling
				try {
					apCustomField = await apCustomfield.create(createData);
				} catch (apiError) {
					// If API call fails, try to fall back to TEXT type
					const errorMessage = apiError instanceof Error ? apiError.message : String(apiError);

					if (createData.dataType === "SINGLE_OPTIONS" || createData.dataType === "MULTIPLE_OPTIONS") {
						console.log(
							`[${requestId}] Custom field API_error_fallback: ${field.label}`,
							{
								originalError: errorMessage,
								attemptingFallback: "TEXT"
							}
						);

						// Retry with TEXT type
						const fallbackData: APPostCustomfieldType = {
							name: createData.name,
							dataType: "TEXT",
							model: createData.model,
						};

						try {
							apCustomField = await apCustomfield.create(fallbackData);
							console.log(
								`[${requestId}] Custom field fallback_success: ${field.label}`,
								{
									fallbackType: "TEXT"
								}
							);
						} catch (fallbackError) {
							// If fallback also fails, log and skip this field
							console.error(
								`[${requestId}] Custom field fallback_failed: ${field.label}`,
								{
									originalError: errorMessage,
									fallbackError: fallbackError instanceof Error ? fallbackError.message : String(fallbackError)
								}
							);
							continue; // Skip this field
						}
					} else {
						// For non-option fields, just log and skip
						console.error(
							`[${requestId}] Custom field creation_failed: ${field.label}`,
							{
								error: errorMessage,
								dataType: createData.dataType
							}
						);
						continue; // Skip this field
					}
				}

				// Add to our local cache to avoid duplicate creation
				apCustomFields.push(apCustomField);
			}

			mappings.push({
				id: apCustomField.id,
				value: value,
			});

			const truncatedValue = value.length > 50 ? value.substring(0, 50) + '...' : value;
			logCustomField(requestId, "mapped", field.label, {
				apFieldId: apCustomField.id,
				value: truncatedValue
			});
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : String(error);
			logError(requestId, `Failed to map field ${field.name} (${field.label})`, error);

			// Log detailed error information for debugging
			logDebug(requestId, `Field mapping error details`, {
				fieldName: field.name,
				fieldLabel: field.label,
				fieldType: field.type,
				allowedValuesCount: field.allowedValues?.length || 0,
				currentValue: value ? `${value.substring(0, 100)}${value.length > 100 ? '...' : ''}` : 'empty',
				errorMessage
			});

			// Continue with other fields rather than failing completely
		}
	}

	return mappings;
}
